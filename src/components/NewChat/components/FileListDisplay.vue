<template>
  <div v-if="files && files.length" class="files-container">
    <div class="files-header">
      <span class="header-title">已选文件</span>
      <span class="header-count">{{ files.length }}个</span>
    </div>
    <div class="files-list">
      <div v-for="(file, index) in files" :key="index" class="file-item">
        <img :src="require('@/assets/images/otherfile.png')" alt="file icon" class="file-icon" />
        <span class="file-name" :title="file">{{ file }}</span>
        <i class="el-icon-close file-remove" @click="removeFile(index)"></i>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';

export default {
  name: 'FileListDisplay',
  computed: {
    ...mapState('common', ['files']),
  },
  methods: {
    ...mapMutations('common', ['REMOVE_FILE']),
    removeFile(index) {
      this.REMOVE_FILE(index);
    },
  },
};
</script>

<style scoped lang="scss">
.files-container {
  padding: 10px 20px;
  background-color: #f7f8fb;
  border-bottom: 1px solid #e8e8e8;
}

.files-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .header-title {
    font-weight: 500;
    color: #313233;
  }

  .header-count {
    margin-left: 8px;
    font-size: 12px;
    color: #949699;
    background-color: #e9ebf0;
    padding: 2px 6px;
    border-radius: 10px;
  }
}

.files-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 10px;
  max-height: 150px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 8px;
  transition: box-shadow 0.2s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .file-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    flex-shrink: 0;
  }

  .file-name {
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: #636466;
  }

  .file-remove {
    margin-left: 8px;
    cursor: pointer;
    color: #949699;
    flex-shrink: 0;

    &:hover {
      color: #f56c6c;
    }
  }
}
</style>
