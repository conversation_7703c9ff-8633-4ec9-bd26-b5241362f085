<template>
  <div
    class="chatMain"
  >
    <div class="cardList" ref="chatContainer">
      <div v-for="(item, index) in messages" :key="item.id" style="width: 100%;">
          <UserMessage v-if="item.type === 'user'">{{ item.answer }}</UserMessage>
          <AssistantMessage v-else :message="item" :message-index="index" @delete="delAns" @like="handleGood({ item, index })">
             <div class="answer" ref="resizeTarget">
                <div v-if="!item.done && !item.answer" class="done">
                  <div class="text">AI正思考中......</div>
                  <PulseLoader :loading="true" size="12px" />
                </div>
                <div v-else style="width: 100%">
                    <div class="text" v-html="item.answer"></div>
                    <div v-if="item.isStreaming" class="streaming-indicator"></div>
                </div>
             </div>
          </AssistantMessage>

        <div v-if="item.helps && item.helps.length" class="tips">
          <el-tag
            class="tag-item"
            v-for="(tag, tagindex) in item.helps"
            :key="tagindex"
            @click="tipsQuestion(tag, index)"
            effect="plain"
            >{{ tag }}</el-tag
          >
        </div>
      </div>

      <WelcomeMessage v-show="!messages.length" :subtitle="remark" />
      <CommonQuestions
        v-show="!messages.length"
        :knowledgeName="knowledgeName"
        @getUseFaq="handleCommonQuestionClick"
      />
    </div>
    <FileListDisplay />
    <KnowledgeSelect ref="KnowledgeSelect" v-show="!this.$store.state.common.files.length" @getknowledgeValue="(val) => this.$emit('getknowledgeValue', val)" />
    <InputChat
      ref="InputChat"
      :isComplete="isComplete"
      :chatSet="chatSet"
      @message-sent="$emit('updataAskList')"
    />
  </div>
</template>

<script>
import { Base64 } from "js-base64";
import { mapState } from 'vuex';
import Tools from "./Tools.vue";
import PulseLoader from "vue-spinner/src/PulseLoader.vue";
import UserMessage from "./UserMessage.vue";
import AssistantMessage from "./AssistantMessage.vue";
import WelcomeMessage from "./WelcomeMessage.vue";
import CommonQuestions from "./CommonQuestions.vue";
import KnowledgeSelect from "./KnowledgeSelect.vue";
import FileListDisplay from "./FileListDisplay.vue";
import { getSystemConfig } from "@/api/login";
import InputChat from "./InputChat.vue";
import serviceKnowledge from "@/api/knowledge.js";
import knowledgeApi from "@/api/knowledge.js";
import { getDocIdDetail } from "@/api/techdocmanage/workflow/learn/index.js";
import PlotlyChart from "./PlotlyChart.vue";
import GraphChart from "./graphChart.vue";
import { parseMarkdown, firstLetterToUpperCase, processDocs } from "../utils";
import userService from "@/api/techdocmanage/docCenter/user";
import { isSqlStatement } from "@/utils/coalmine.js";
import { markdownTable } from "markdown-table";
import {
  previewFileFn,
  donwloadFileFn,
  sendBigFileQuestion,
  sendQuestion,
  getConfigKey,
} from "./commonReq.js";
import ClipboardJS from "clipboard";
import { EventSourcePolyfill } from "event-source-polyfill";
import { getToken } from "@/utils/auth";
import { preview } from "@/utils/fileStreamPreview";
export default {
  name: "",
  components: {
    PulseLoader,
    Tools,
    InputChat,
    PlotlyChart,
    GraphChart,
    WelcomeMessage,
    CommonQuestions,
    UserMessage,
    AssistantMessage,
    KnowledgeSelect,
    FileListDisplay,
  },
  props: {
    showAppend: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 保留必要的本地状态
      knowledgeName: "",
      chatSet: null,
      remark: "作为你的智能伙伴，我既能写文案、想点子，又能陪你聊天、答疑解惑。",
      isComplete: true, // 默认完成状态
    };
  },
  computed: {
    ...mapState('new-chat', ['messages', 'isSending'])
  },
  watch: {
    messages: {
      handler() {
        this.scrollToBottom();
      },
      deep: true
    }
  },
  created() {
    getSystemConfig().then((res) => {
      this.img = res.data.logoPic;
    });
    getConfigKey("previewFile").then((response) => {
      this.initCurrentIP = response.msg;
    });
    getConfigKey("temperature").then((response) => {
      this.temperature = response.msg;
    });
    getConfigKey("kkFileView").then((response) => {
      this.kkFileView = response.msg;
    });
  },
  mounted() {},
  methods: {
    // 初始化知识库
    initData(val) {
      this.chatSet = val;
      this.remark =
        val.remark ||
        "作为你的智能伙伴，我既能写文案、想点子，又能陪你聊天、答疑解惑。";
      this.knowledgeName = val.knowledge_name;
    },
    // 清空消息
    clearMessages() {
      this.$store.commit('new-chat/CLEAR_MESSAGES');
    },
    // 处理来自 CommonQuestions 组件的点击事件
    async handleCommonQuestionClick(question) {
      await this.$store.dispatch('new-chat/sendMessage', { text: question, chatSettings: this.chatSet });
      this.$emit('updataAskList');
    },
    //  自动滚动
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.chatContainer;
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      });
    },
    delAns(message) {
      this.$store.commit('new-chat/DELETE_MESSAGE', message.id);
    },
    async handleGood({ item, index }) {
      const liked = !item.liked;
      this.$store.commit('new-chat/UPDATE_MESSAGE', { id: item.id, updates: { liked } });

      try {
        const response = await knowledgeApi.giveThumbsUp({
          id: item.historyId,
          likeValue: liked ? 1 : 0
        });

        if (response.code !== 200) {
          this.$store.commit('new-chat/UPDATE_MESSAGE', { id: item.id, updates: { liked: !liked } });
          console.error('点赞失败:', response.msg);
        }
      } catch (error) {
        this.$store.commit('new-chat/UPDATE_MESSAGE', { id: item.id, updates: { liked: !liked } });
        console.error('点赞请求失败:', error);
      }
    }
  },
  destroyed() {
    // this.$refs.myPopover.doClose();
  },
};
</script>

<style scoped lang="scss">
@import "@/assets/styles/chatCommon.scss";
.chatMain {
  flex: 1;
  padding: 20px;
  background-color: #f7f8fb;
  display: flex;
  flex-direction: column;
  // overflow-x: hidden;
  .cardList {
    width: 100%;
    flex: 1;
    overflow: auto;
    max-height: calc(100% - 100px);
	min-height: 300px;
    margin-bottom: 10px;
    .box-card {
      min-height: 150px;
      margin: 20px 40px 20px 40px;
	  width: calc(100% - 80px);
      .question {
        display: flex;
        width: 100%;
        border-bottom: 1px solid #d6cccc;
        padding-bottom: 10px;
        margin-bottom: 10px;
        img {
          width: 30px;
        }
        asks-Items {
          font-size: 16px;
          height: 30px;
          line-height: 30px;
          margin-left: 10px;
          width: 100%;
          min-height: 28px;
          box-sizing: border-box;
          white-space: pre-wrap;
          flex: 1;
          // margin: 0 10px;
          // position: relative;
          // overflow-x: scroll;
        }
      }
      .answer {
        display: flex;
        width: 100%;
        .imgbg {
          width: 30px;
          height: 30px;
          background: #10a37f;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          img {
            width: 25px;
          }
        }
        .done {
          width: 100%;
          display: flex;
          align-items: center;
        }
        .text {
          margin-left: 10px;
          white-space: pre-line;
        }
        .result {
          margin-top: 20px;
        }
        .result-item {
          margin-right: 10px;
        }
      }
    }
  }
}

// p {
//   margin: 0 !important;
// }

.key_input {
  margin-right: 20px;
  border: 1px solid #10a37f;
  padding: 10px 20px;
  border-radius: 8px;
}

.now_key {
  margin-top: 20px;
  background-color: #f3fdfb;
  border: 1px solid #10a37f;
  padding: 10px 20px;
  font-size: 16px;
  color: #10a37f;
  border-radius: 8px;
}

.qr_box {
  padding: 10px;
  margin: 20px auto;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 240px;
    margin: 0 10px;
    padding: 10px;
    background-color: #fff;
    border: 1px solid #10a37f;
    border-radius: 8px;
  }
}

.show_qr {
  font-weight: bold;
  cursor: pointer;
  color: #10a37f;
}

@media screen and (max-width: 700px) {
  .qr_box {
    img {
      width: 150px;
    }
  }
}
::v-deep .v-spinner {
  padding-top: 5px;
  margin-left: 5px;
}
.typing {
  width: 100%;
  height: auto;
  // max-height: 300px;
  // overflow: auto;
}
/* 打字机 */
.typewriter {
  width: 1000px;
  margin: auto;
  background-color: #eeeeee;
  box-sizing: border-box;
  padding: 100px 80px;
  height: 100%;
  position: relative;
  overflow: hidden;
}
.ask-docs {
  width: 100%;
  min-width: 600px;
}
::v-deep .el-loading-mask {
  // background-color: rgb(231 239 255 / 38%)
  background-color: rgba(231, 239, 255, 0.38) !important;
}
.files {
  height: 65px;
  max-width: 800px;
  width: 100%;
  pointer-events: auto;
  border-radius: 8px;
  padding: 5px;
  overflow: auto;
  margin-bottom: 10px;
  display: grid;
  // grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  grid-gap: 10px;
  justify-items: center;
  overflow-x: hidden;
  .filesItem {
    display: flex;
    align-items: center;
    .text {
      width: 400px; /* 设置容器宽度 */
      white-space: nowrap; /* 防止文本换行 */
      overflow: hidden; /* 文本溢出容器时隐藏 */
      text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
    }
    img {
      width: 20px;
    }
  }
}

.tools {
  display: flex;
  justify-content: space-between;
}
.thumbs-up {
  display: flex;
  align-items: center;
}
.tips {
  display: flex;
  flex-direction: column;
  margin: 0 40px;
  .tag-item {
    width: 400px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 10px;
    cursor: pointer;
  }
}

::v-deep .el-tag--plain {
  background-color: white;
  border-color: #fff !important;
  color: #5d5858 !important;
}
/* ul ol 样式 */
::v-deep ul,
ol {
  margin: 10px 0 10px 20px;
}
::v-deep p {
  margin: 0px !important;
}
.cust {
  width: 100%;
}
//  ::v-deep cust{
::v-deep table {
  width: 100%;
  border: 1px solid #ccc;
}
::v-deep table td,
table th {
  border-bottom: 1px solid #ccc !important;
  border-right: 1px solid #ccc !important;
  padding: 5px 10px !important;
}
::v-deep table th {
  // border-bottom: 2px solid #ccc;
  text-align: center;
  background: #dee8ee;
}
::v-deep table tr {
  // border-bottom: 2px solid #ccc;
  text-align: center;
}
::v-deep table th:last-child {
  border-right: none;
}
::v-deep table td:last-child {
  border-right: none;
}

::v-deep table tr:last-child td {
  border-bottom: none;
}
::v-deep tr:nth-child(even) {
  background: #eff3f5;
}
.file-operation-item {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-end;
}

::v-deep .fileName-link {
  cursor: pointer;
  color: #409eff;
}
::v-deep ul {
  padding-left: 0px !important;
}
</style>

<style lang="scss">
// .popper-file-operation .el-popover {
//     min-width: 0px !important;
//         background: aliceblue !important;
// }
// .popper-file-operation[x-placement^='bottom'] .popper__arrow {
//     border-bottom-color: #f56e48 !important;
// }

// .popper-file-operation[x-placement^='bottom'] .popper__arrow::after {
//     border-bottom-color: #f56e48 !important;
// }
.popper-file-operation.el-popover {
  // color: white;
  max-width: 400px;
  background: aliceblue !important;
  border-color: aliceblue !important;
  min-width: 0px !important;
  cursor: pointer;
}
.doc-item {
  padding: 12px;
  border-bottom: 1px solid #ebeef5;

  &:last-child {
    border-bottom: none;
  }

  .doc-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .doc-index {
      margin-right: 8px;
    }
  }

  .doc-content {
    margin-top: 10px;
    margin-left: 20px;
  }
}

::v-deep code {
  white-space: pre-wrap; /* CSS3 */
  word-wrap: break-word; /* IE6-7 */
}
</style>
