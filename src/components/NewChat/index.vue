<template>
  <div class="container">
    <z-dialog
      :title="chatDialog.titleChat"
      :show.sync="chatDialog.openChat"
      ref="zdialog"
      v-if="chatDialog.openChat"
      @closeOnClickModal="closeOnClickModal"
      @handleMinimize="handleMinimize"
      :isFooter="false"
    >
      <div slot="body" class="content">
        <!-- 知识库选择 -->
        <div class="contentMain">
          <!-- 左侧对话列表 历史记录  功能 -->
          <LeftOptionList
            ref="LeftOptionList"
            @getUseFaq="getUseFaq"
            @getOptions="getOptions"
            @getHistory="getHistory"
            @updataTopK="updataTopK"
            @new-chat="handleNewChat"
          />
          <!-- 对话框主体 -->
          <ChatMain
            ref="ChatMain"
            @updataAskList="updataAskList"
            @handleMinimize="
              () => {
                this.$refs.zdialog.minimize();
              }
            "
            @getknowledgeValue="getknowledgeValue"
          />
		  <RightOptionList></RightOptionList>
        </div>
      </div>
      <div slot="footer">对话框footer</div>
    </z-dialog>
  </div>
</template>

<script>
import ZDialog from "@/components/ZDialog";
import ChatMain from "./components/ChatMain.vue";
import LeftOptionList from "./components/LeftOptionList.vue";
import RightOptionList from "./components/RightOptionList.vue";
import { getChatSetting } from "./components/commonReq.js";
import knowledgeApi from "@/api/knowledge.js";

export default {
  name: "",
  components: { ZDialog, LeftOptionList, ChatMain, RightOptionList },
  props: {
    oldChat:{
      type:String
    }
  },
  data() {
    return {
      // 聊天对话框配置
      chatDialog: {
        titleChat: "知识库AI助手",
        openChat: false,
        isShrink: true,
        chatParams: {
          knowledge_base_name: "",
          knowledgeList: [],
        },
      },
      flag: "",
      knowledge_name: "",
      setKnowledge: "",
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    open() {
      this.chatDialog.openChat = true;
      // 等待弹窗完全打开后设置为全屏
      this.$nextTick(() => {
        if (this.$refs.zdialog) {
          // this.$refs.zdialog.setFullscreen();
        }
      });
    },
    //关闭最小化弹框
    closeMinimize() {
      this.$refs.zdialog.closeDialog();
      setTimeout(() => {
        this.chatDialog.openChat = true;
        // 等待弹窗完全打开后设置为全屏
        this.$nextTick(() => {
          if (this.$refs.zdialog) {
            this.$refs.zdialog.setFullscreen();
          }
        });
      }, 200);
    },
    handleMinimize() {
      this.$emit("handleMinimize");
      // if (this.$store.state.common.isEnterpriseKnowledge) {
      //         this.$refs.KnowledgeSelect&&this.$refs.KnowledgeSelect.getList();
      // }
      // console.log(this.$store.state.common.isEnterpriseKnowledge);
      // 确保对话框状态正确关闭
      this.chatDialog.openChat = false;

      if (this.$store.state.common.isEnterpriseKnowledge) {
        this.$refs.KnowledgeSelect && this.$refs.KnowledgeSelect.getList();
      }

    },
    closeOnClickModal() {
      this.chatDialog.openChat = false;
      this.$emit("closeOnClickModal");
      this.$store.commit("common/isEnterpriseKnowledge", false);
    },
    // 获取选中知识库
    async getknowledgeValue(knowledge) {
      let knowledge_name = knowledge.label;
      const files = this.$store.state.common.files;

      if (files.length > 0 && this.oldChat) {
        knowledge_name = this.oldChat;
      }

      // 获取聊天初始化配置
      const { code, data } = await getChatSetting(knowledge_name);
      if (code === 200) {
        this.knowledge_name = knowledge_name;
        this.setKnowledge = data;
        const settings = { ...data[0], knowledge_name };
        this.$store.commit("new-chat/SET_CHAT_SETTINGS", settings);
        this.$store.commit("chat-v3/SET_CURRENT_KNOWLEDGE", {
          ...knowledge,
          image: this.$refs.ChatMain.$refs.KnowledgeSelect.getImageUrl(
            knowledge.img
          ),
        });
        this.$refs.ChatMain.clearMessages();
        this.$refs.LeftOptionList.initData(settings);
        this.$refs.ChatMain.initData(settings);
        if (this.$store.state.common.files.length > 0) {
          this.$refs.LeftOptionList.getList();
        }

        // 如果是通过文件对话启动，则尝试加载历史记录
        if (files.length > 0) {
          const fileNameQuery = files.join(',');
          const historyRes = await knowledgeApi.getHistoryList({
            knowledge_name: knowledge_name,
            query: fileNameQuery,
            pageNum: 1,
            pageSize: 1,
            generate: "0",
          });

          if (historyRes.code === 200 && historyRes.rows.length > 0) {
            const historyItem = historyRes.rows[0];
            await this.$store.dispatch('new-chat/selectChat', historyItem.id);
            // 选中左侧列表的对应项
            this.$refs.LeftOptionList.currentIndex = 0;
          } else {
            console.log("未找到与文件相关的历史对话，开启新对话。");
          }
        }
      }
    },
    // 常见问题回调
    getUseFaq(val) {
      this.$refs.ChatMain.askQuestion(val, "0");
    },
    // 功能中心回调
    getOptions(val) {
      this.$refs.ChatMain.askQuestion(val, "1");
    },
    // 对话列表回调
    async getHistory(historyItem) {
      try {
        await this.$store.dispatch('new-chat/selectChat', historyItem.id);
      } catch (error) {
        console.error('获取历史记录详情失败:', error);
        this.$message.error('获取历史记录详情失败');
      }
    },
    handleNewChat() {
      this.$refs.ChatMain.clearMessages();
    },
    // 更新左侧对话列表
    updataAskList() {
      this.$refs.LeftOptionList.getList();

    },
    updataTopK(val) {
      const { top_k } = val;
      let params = {
        ...this.setKnowledge[0],
        top_k,
        knowledge_name: this.knowledge_name,
      };
      this.$refs.LeftOptionList.initData(params);
      this.$refs.ChatMain.initData(params);
    },
  },
};
</script>

<style scoped lang="scss">
.content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .contentMain {
    flex: 1;
    min-height: 0; // 确保flex子元素可以收缩
    width: 100%;
    display: flex;
    overflow: hidden;
  }
}
</style>
