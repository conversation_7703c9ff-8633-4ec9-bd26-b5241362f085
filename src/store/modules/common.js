const common = {
  namespaced: true,
  state: {
    pageOfficeClose: false,
    previewFileId: null,
    keyword: null,
    chat:'',
    chatLoading:false,
    files:[],
    cardListFn:false,
    isDatabaseChat:false,
    isGraphdataBaseChat:false,
    isMedicalAtlasChat:false,
    isMinimize:false,
    inputValue:'',
    isEnterpriseKnowledge:false,
    configData: new Map(),
    // Chat V3 相关状态
    chatV3Open: false,
    chatV3Knowledge: '',
  },

  mutations: {
    pageOfficeClose(state, str) {
      state.pageOfficeClose = str;
    },
    PDFDOCID(state, str) {
      state.previewFileId = str;
    },
    PDFDOCKEYWORD(state, str) {
      state.keyword = str;
    },
    CHAT(state, str) {
      state.chat = str;
    },
    CHATLOADING(state, str){
      state.chatLoading = str;
    },
    FILES(state, str){
      state.files = str;
    },
    cardListFn(state, str){
      state.cardListFn = str;
    },
    REMOVE_FILE(state, index) {
      state.files.splice(index, 1);
    },
    isDatabaseChat(state, str){
      state.isDatabaseChat = str;
    },
    isGraphdataBaseChat(state, str){
      state.isGraphdataBaseChat = str;
    },
    isMedicalAtlasChat(state, str){
      state.isMedicalAtlasChat = str;
    },
    isMinimize(state, str){
      state.isMinimize = str;
    },
    inputValue(state, str){
      state.inputValue = str;
    },
    isEnterpriseKnowledge(state, str){
      state.isEnterpriseKnowledge = str;
    },
    configData(state, {id,data}){
      console.log(id,data);

      state.configData.set(id,data);
    },
    // Chat V3 相关mutations
    CHAT_V3_OPEN(state, str){
      state.chatV3Open = str;
    },
    CHAT_V3_KNOWLEDGE(state, str){
      state.chatV3Knowledge = str;
    }
  },

  actions: {},
};

export default common;
