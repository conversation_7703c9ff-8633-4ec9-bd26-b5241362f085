<template>
  <div>
    <!-- 归档文件Dialog -->
    <el-dialog
      title="文件归档"
      :visible.sync="showArchive"
      width="640px"
      :close-on-click-modal="false"
      @closed="closeArchive"
    >
      <el-form
        ref="archiveForm"
        :model="archiveForm"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="归档名称" prop="archiveId">
          <el-select
            v-model="archiveForm.archiveId"
            placeholder="请选择归档名称"
            @change="getArchiveId"
          >
            <el-option
              v-for="(item, index) in archiveList"
              :key="index"
              :label="item.archiveName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="归档路径">
          <el-input v-model="archiveForm.path" disabled></el-input>
        </el-form-item>
        <el-form-item label="归档期限">
          <el-radio-group v-model="timeType">
            <el-radio :label="1">永久</el-radio>
            <el-radio :label="2">
              <el-date-picker
                v-model="archiveForm.archiveTime"
                :disabled="timeType!==2"
                type="datetimerange"
                align="center"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                :picker-options="pickerOptions"
              />
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="归档方式" prop="func">
          <el-radio-group v-model="archiveForm.func">
            <el-radio label="1">直接归档</el-radio>
            <el-radio label="2" disabled>审批归档</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="归档人">
              <el-input v-model="archiveForm.person" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归档部门">
              <el-input v-model="archiveForm.dept" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeArchive">取 消</el-button>
        <el-button type="primary" @click="submitArchive">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 移动文件Dialog -->
    <el-dialog
      :title="isMove ? '移动到' : '加入我的文件'"
      :visible.sync="showMove"
      width="520px"
      :close-on-click-modal="false"
    >
      <div class="tree-wrap">
        <el-tree
          v-if="showMove"
          ref="tree"
          empty-text="暂无文件夹数据"
          :data="treeData"
          :props="defaultProps"
          node-key="id"
          @node-click="handleNodeClick"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span>
              <i
                :class="
                  data.docType === 'directory'
                    ? 'el-icon-folder-opened'
                    : 'el-icon-document'
                "
                style="margin-right: 5px"
              />{{ node.label }}
            </span>
          </span>
        </el-tree>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showMove = false">取 消</el-button>
        <el-button type="primary" @click="submitMove">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 签入签出---start -->
    <el-dialog
      title="签出"
      :visible.sync="checkOutIn"
      width="520px"
      :close-on-click-modal="false"
    >
      <div>
        <h3 style="font-weight: 900">请启动桌面助手来完成此操作</h3>
        <h4>桌面助手可增强浏览器的功能，如可靠上传、文档编辑等</h4>
        <div class="checkOutIn">
          <div class="check">
            <div>已安装桌面助手</div>
            <el-button @click="oneTouchStart">一键启动</el-button>
            <div>可以在菜单中找到并点击启动桌面助手</div>
          </div>
          <el-divider direction="vertical"></el-divider>
          <div class="check">
            <div>如果没有下载请点击下方下载按钮</div>
            <el-button @click="dowloadClick">下载安装</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- 签入签出---end -->
    <!-- 分享详情---start -->
    <el-dialog
      title="分享详情"
      :visible.sync="shareDetailShow"
      width="820px"
      :close-on-click-modal="false"
    >
      <el-table :data="shareTableData" stripe style="width: 100%">
        <el-table-column type="index" label="序号" width="50">
        </el-table-column>
        <el-table-column prop="shareBy" label="分享者" width="180">
        </el-table-column>
        <el-table-column prop="receiveBy" label="被分享者" width="180">
        </el-table-column>
        <el-table-column prop="shareTime" label="分享时间"> </el-table-column>
        <el-table-column prop="endTime" label="失效时间"> </el-table-column>
      </el-table>
    </el-dialog>
    <!-- 分享详情---end -->
    <detail-view ref="detailView"></detail-view>
    <share-view ref="shareView" @shareSet="shareSet"></share-view>

    <!-- 原文件下载和pdf下载选择框 -->
    <el-dialog center title="下载" :visible.sync="visible" width="20%">
      <div style="display: flex; justify-content: center">
        <el-button type="success" size="mini" @click="downloadType(0)"
        >原文件下载</el-button
        >
        <!-- <el-button type="primary" size="mini" @click="downloadType(1)"
          >pdf下载</el-button
        > -->
      </div>
    </el-dialog>
    <!-- 标签设置组件-->
    <LabelSet ref="labelOpen" @getListChild="getListChild"></LabelSet>
  </div>
</template>

<script>
/**
 * 表格类型，不同类型表示不同的按钮组
 * 0——我的文件——移动0、下载1、删除2、分享3、归档4、详细信息5
 * 1——我的收藏——加入我的文件(移动)0、下载1、移除收藏2、详细信息3
 * 2——我的分享——下载0、删除分享1
 * 3——分享给我——下载0
 * 4——回收站——还原0、彻底删除1
 * 5——归档成果——签入0、签出1
 * 6——最近浏览——收藏0、下载1
 */
import LabelSet from "../labelSet.vue";
import { getUserProfile } from "@/api/system/user";
import userService from "@/api/techdocmanage/docCenter/user";
import detailView from "../detailView.vue";
import shareView from "../shareView.vue";
import { createSocket } from "@/utils/socket.js";
import { getConfigKey } from "@/api/system/config";

import serviceKnowledge from "@/api/knowledge.js";
export default {
  props: ["tableStyle", "treeData", "leftCheckedNode", "rightCheckedList"],
  components: {
    detailView,
    shareView,
    LabelSet,
  },
  data() {
    return {
      visible: false,
      downloadData: {},
      // 分享详情
      shareDetailShow: false,
      shareTableData: [],
      // 签入签出
      checkOutIn: false,
      statusclickTr: true,
      isMove: true,
      showMove: false,
      defaultProps: {
        children: "children",
        label: "userDocName",
        isLeaf: "folderFlag",
      },
      rules: {
        archiveId: [
          { required: true, message: "请输入活动名称", trigger: "blur" },
        ],
        archiveTime: [
          { required: true, message: "请选择日期", trigger: "blur" },
        ],
        func: [
          { required: true, message: "请选择活动资源", trigger: "change" },
        ],
      },
      showArchive: false,
      archiveForm: {
        archiveId: "",
        archiveTime: [],
        func: "1",
        path: "",
      },
      timeType:1,
      archiveList: [],
      // 移动弹框树点击的数据
      fileMove: {},
      // 移动文件id
      moveID: "",
      // 分享文档Id
      shareID: "",
      // 归档文档Id
      archiveId: "",
      // 我的收藏父级Id
      collectParentId: "",
      // 我的收藏基础id
      collectId: "",
      // 登录用户信息
      userInfo: {},
      // 服务ip
      initCurrentIP: "",
      errorSetInerval: "",
      // 签出id
      checkdata: {},
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() <= new Date().setHours(0, 0, 0, 0) - 1;
        },
        shortcuts: [
          {
            text: "一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      embed_model: "",
    };
  },
  watch: {
    rightCheckedList: {
      handler(val) {
        if (val.length == 0) {
          this.visible = false;
        }
      },
    },
    timeType:{
      handler(val){
        if (val==1) {
          console.log(this.archiveForm.archiveTime);
          this.archiveForm.archiveTime = []
        }else{

        }
      }
    }
  },
  created() {
    this.init();
  },
  mounted() {
    getConfigKey("fileAssistantDownload").then((response) => {
      this.initCurrentIP = response.msg;
    });
    getUserProfile().then((response) => {
      this.userInfo = response.data;
    });
    getConfigKey("embed_model").then((response) => {
      this.embed_model = response.msg;
    });
  },
  methods: {
    /** 查看详细信息 */
    viewDetails(row) {
      this.$refs.detailView.open(row);
    },
    /** 触发函数 */
    trigger(e) {
      switch (this.tableStyle) {
        case 0:
          if (e[1] === 0) {
            this.moveFile(e[0]);
            this.isMove = true;
            this.showMove = true;
          } else if (e[1] === 1) {
            if (e instanceof Array) {
              this.visible = true;
              this.downloadData = e[0];
            } else {
              this.downloadFile(e[0]);
            }
          } else if (e[1] === 2) {
            this.deleteFile(e[0]);
          } else if (e[1] === 3) {
            this.shareFile(e[0]);
          } else if (e[1] === 4) {
            this.archiveFile(e[0]);
          } else if (e[1] === 5) {
            this.$refs.detailView.open(e[0][0], false);
          } else if (e[1] === 6) {
            let data = {
              docList: e[0],
              // labelType: 1 个人文档   2 知识体系  3培训专区
              labelType: 1,
            };
            this.$refs.labelOpen.open(data);
          } else if (e[1] === 99) {
            console.log(e[0]);
            // 1查询是否存在选中文件的临时知识库 ：
            //  1.1 存在    则查询临时知识库下的文件列表是否存在选中文件
            //  1.1.2 存在  则打开对话框
            //  1.1.3 不存在  则再此临时知识库下重新上传此文件
            //  1.2 不存在 则再创建
            // 2创建临时知识库- template-Date.now()
            // 3下载文件获取文件流  将文件流转成file对象
            // 4将文件对象上传给临时知识库并进行概述
            // this.createKnoledge(e[0]);
            this.queryFilesKnoledge(e[0]);
          }
          break;
        case 1:
          if (e[1] === 0) {
            this.addCollect(e[0]);
            this.isMove = false;
            this.showMove = true;
          } else if (e[1] === 1) {
            this.downloadFile(e[0]);
          } else if (e[1] === 2) {
            this.$confirm("是否确定移除收藏？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "error",
            }).then(() => {
              this.removeCollection(e[0]);
            });
          } else if (e[1] === 3) {
            this.$refs.detailView.open();
          }
          break;
        case 2:
          if (e[1] === 0) {
            this.downloadFile(e[0]);
          } else if (e[1] === 1) {
            this.$confirm(
              "删除后将会从被分享者列表中移除，是否删除分享？",
              "提示",
              {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "error",
              }
            ).then(() => {
              this.deleteShare(e[0]);
            });
          } else if (e[1] === 2) {
            if (e[0].length == 1) {
              this.shareDetails(e[0][0]);
            } else {
              this.$message.warning("暂不支持多文件操作");
            }
          }
          break;
        case 3:
          if (e[1] === 0) {
            this.shareToMe(e[0]);
          }
          break;
        case 4:
          if (e[1] === 0) {
            this.$confirm("还原后将恢复至原文件夹下，是否还原？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(() => {
              this.restoreFile(e[0]);
            });
          } else if (e[1] === 1) {
            this.$confirm("彻底删除后将无法找回，是否彻底删除？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "error",
            }).then(() => {
              this.deleteFile2(e[0]);
            });
          }
          break;
        case 5:
          if (e[1] === 1) {
            //
            // if (e[0].length == 1) {
            //   this.checkout(e[0][0]);
            // }else{
            this.$message.warning(
              "暂不支持多文件操作，如是单文件操作请点击文件后面按钮操作"
            );
            // }
          } else if (e[1] === 4) {
            // this.init()
            this.checkout(e[0][0]);
          }
          break;
        case 6:
          if (e[1] === 0) {
            this.$confirm("选中文件将添加至 个人文档 - 我的收藏 列表", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(() => {
              this.addCollect2(e[0]);
            });
          } else if (e[1] === 1) {
            this.downloadFile(e[0]);
          }
          break;
        case 99:
          break;
        default:
          break;
      }
    },
    // 查询是否存在选中文件的临时知识库
    async queryFilesKnoledge(value) {
      const files = value.map((v) => v.userDocName).join(",");
      const { code, data } = await serviceKnowledge.queryFilesKnoledge({
        files,
      });

      if (code === 200) {
        if (data.knowledge_name) {
          //  则查询临时知识库下的文件列表
          const res = await serviceKnowledge.getKnowledgeFileList({
            knowledge_base_name: data.knowledge_name,
          });
          if (res.code === 200) {
            console.log(res);
            // 选中的文件名是否都存在临时知识库下的文件列表
            let filesArr = files.split(",");
            const selectFileExistTemBase = filesArr.every((v) =>
              res.data.includes(v)
            );
            // let missingFiles = filesArr.filter((v) => !res.data.includes(v));
            if (selectFileExistTemBase) {
              this.$store.commit("common/CHATLOADING", false);
              this.$store.commit("common/CHAT", data.knowledge_name);
              this.$store.commit("common/FILES", files.split(","));
            } else {
              this.$store.commit("common/CHATLOADING", true);
              this.$store.commit("common/CHAT", data.knowledge_name);
              this.$store.commit("common/FILES", files.split(","));
              this.getFileObj(value, data.knowledge_name);
            }
          }else{
            this.createKnoledge(value,data.knowledge_name);
          }

          // this.getFileObj(value, data.knowledge_name);
        } else {
          console.log("不存在临时知识库");
          this.createKnoledge(value);
        }
      }
    },
    // 创建知识库
    async createKnoledge(value,knowledge_name="") {
      console.log("创建知识库")
      const knowledge_base_name =knowledge_name?knowledge_name: `template-${Date.now()}`;
      const files = value.map((v) => v.userDocName);
      this.$store.commit("common/CHATLOADING", true);
      this.$store.commit("common/CHAT", knowledge_base_name);
      this.$store.commit("common/FILES", files);
      //新增临时知识库
      const res = await serviceKnowledge.insertKnowledgeDatabase({
        knowledge_name: knowledge_base_name,
        files: files.join(","),
      });

      const { code, data, msg } = await serviceKnowledge.addKnowledgeList({
        knowledge_base_name,
        vector_store_type: "faiss",
        // embed_model: "bge-large-zh-v1.5",
        embed_model: this.embed_model,
      });
      if (code === 200) {
        // 下载文件获取文件流  将文件流转成file对象
        this.getFileObj(value, knowledge_base_name);
      } else {
        this.$message.error(msg);
      }
    },
    // 下载文件获取文件流  将文件流转成file对象
    getFileObj(value, knowledge_base_name) {
      // 创建一个 Promise 数组
      let promises = [];

      const downloadSource = (data) => {
        // 返回一个新的 Promise
        return userService
          .downloadSourceFile(data)
          .then((res) => {
            let blob = new Blob([res]);

            // 直接将 Blob 转换为 File 对象，而不是先读取文本内容
            // 注意：这里假设 res 不是 JSON，而是直接的二进制数据
            const file = new File([blob], data.fileName, {
              type: blob.type,
              lastModified: Date.now(),
            });

            // 返回 File 对象
            return file;
          })
          .catch((error) => {
            // 如果出现错误，你可能想要在这里处理它
            console.error("Download failed:", error);
            // 返回一个拒绝状态的 Promise
            return Promise.reject(error);
          });
      };

      if (value && value.length) {
        // 为每个值创建并推送 Promise
        value.forEach((v) => {
          promises.push(
            downloadSource({
              id: v.docId.toString(),
              userId: this.$store.state.user.userId,
              fileName: v.userDocName,
            })
          );
        });

        // 使用 Promise.all 等待所有 Promise 都完成
        Promise.all(promises)
          .then((files) => {
            // files 现在是所有下载的文件对象的数组
            this.addKnoledgeFile(files, knowledge_base_name);
          })
          .catch((error) => {
            console.error(error);
          });
      }
    },
    // 向知识库中插入上传的文件文件
    async addKnoledgeFile(files, knowledge_base_name) {
      const setData = await serviceKnowledge.getChatSetting({
        knowledgeName: knowledge_base_name,
      });

      if (setData.code === 200) {
        let formData = new FormData();
        files.forEach((item) => {
          formData.append("files", item);
        });
        formData.append("knowledge_base_name", knowledge_base_name);
        formData.append("to_vector_store", true);
        formData.append("not_refresh_vs_cache", false);
        formData.append("chunk_size", setData.data[0].chunk_size);
        formData.append("chunk_overlap", 50);
        formData.append("zh_title_enhance", true);
        formData.append("override", true);
        const { code, data } = await serviceKnowledge.uploadFile(formData);
        if (code === 200) {
          this.$store.commit("common/CHATLOADING", false);
          // loading.close();
        }
      }
    },
    downloadType(type) {
      switch (type) {
        case 0:
          let list = this.downloadData;
          let fileType = list.some((val, index, arr) => {
            return arr[index].docType !== "file" ? true : false;
          });
          if (fileType) {
            this.$message.info("选中文件中包含文件夹，无法下载");
          } else {
            list.forEach((item) => {
              let data = {
                id: item.docId.toString(),
                userId: this.$store.state.user.userId,
              };
              userService.downloadSourceFile(data).then((res) => {
                let blob = new Blob([res]);
                blob.text().then((result) => {
                  try {
                    let res = JSON.parse(result);
                    if (res.code === 500) {
                      this.$message.error(res.msg);
                      return;
                    }
                  } catch (error) {
                    const file = new File([blob], "test.docx", {
                      type: "text/plain",
                      lastModified: Date.now(),
                    });

                    let objectUrl = URL.createObjectURL(blob);
                    let link = document.createElement("a");
                    // 源文件下载
                    link.download = item.userDocName;
                    // }
                    link.href = objectUrl;
                    link.click();
                    link.remove();
                    this.visible = false;
                  }
                });
              });
            });
          }
          break;
        case 1:
          this.downloadFile(this.downloadData);
          this.visible = false;
          break;
        default:
          break;
      }
    },
    // 单个文件下载
    downloadFile(row) {
      let fileType = row.some((val, index, arr) => {
        return arr[index].docType !== "file" ? true : false;
      });
      if (fileType) {
        this.$message.info("选中文件中包含文件夹，无法下载");
      } else {
        row.forEach((item) => {
          this.download(item);
        });
      }

      // if (row[0].docType !== 'file') {
      //   this.$message.info('文件夹无法下载!')
      //   return
      // }
      // if (row.length == 1) {
      //   this.download(row[0])
      // } else {
      //   this.$message.info('不支持多文件下载或者不是文件')
      // }
    },
    download(list) {
      console.log("🚀 ~ download ~ list:", list);

      let name = list.userDocName ? list.userDocName : list.resultName;
      let fileName = name.substr(0, name.lastIndexOf("."));

      let suffix = name.substr(name.lastIndexOf(".") + 1);

      let lists = [];
      lists.push(list);
      let data = {
        id: lists[0].docId.toString(),
        userId: this.$store.state.user.userId,
      };
      userService.downloadFile(data).then((res) => {
        let blob = new Blob([res]);
        blob.text().then((result) => {
          try {
            let res = JSON.parse(result);
            if (res.code === 500) {
              this.$message.error(res.msg);
              return;
            }
          } catch (error) {
            let objectUrl = URL.createObjectURL(blob);
            let link = document.createElement("a");
            // if (suffix !== 'jpg') {
            // pdf下载
            link.download = name;
            // } else {
            //   // 源文件下载
            //   link.download = list.resultName
            // }
            link.href = objectUrl;
            link.click();
            link.remove();
          }
        });
      });
    },
    /** 移动弹框树点击事件 */
    handleNodeClick(data, node) {
      this.fileMove = data;
      this.collectParentId = data.id;
    },
    // 文件移动
    moveFile(row) {
      let ids = [];
      row.forEach((element) => {
        ids.push(element.id);
      });
      this.moveID = ids;
    },
    submitMove() {
      if (this.isMove) {
        let data = {
          ids: this.moveID.toString(),
          parent: this.fileMove.id,
        };

        userService.personalFileMove(data).then((res) => {
          if (res.code === 200) {
            this.$message.success("移动成功");
            this.$emit("getTree");
            this.$emit("tableDataList", this.fileMove.id);
            this.showMove = false;
          }
        });
      } else {
        let data = {
          userDocParent: this.collectParentId,
          docId: this.collectId[0].docId,
        };

        userService.personalCollectionAdd(data).then((res) => {
          if (res.code === 200) {
            this.$message.success("加入文件夹成功");
            this.$emit("getTree");
            // this.$emit("getList", this.collectId[0].userDocParent);
            this.showMove = false;
          }
        });
      }
    },
    // 文件删除
    async deleteFile(row) {
      let ids = [];
      let file_names = [];
      row.forEach((element) => {
        ids.push(element.docId);
        file_names.push(element.userDocName);
      });

      let docId = ids.toString();
      userService.personalFileDelete(docId).then((res) => {
        if (res.code === 200) {
          this.$emit("tableDataList", row[0].userDocParent);
          this.$emit("getTree");
          this.$message.success("删除成功");
        }
      });

      const { code, data } = await serviceKnowledge.deleteFile({
        knowledge_base_name: "个人文档-" + this.$store.state.user.nickName,
        file_names,
        delete_content: true,
        not_refresh_vs_cache: false,
      });
    },
    // 文件分享
    shareFile(row) {
      let isChecked = row.every((val, index, arr) => {
        return arr[index].docType == "directory" ? false : true;
      });
      if (isChecked) {
        this.shareID = [];
        row.forEach((element) => {
          this.shareID.push(element.id);
        });
        this.$refs.shareView.showShare = true;
      } else {
        this.$message.warning("文件夹暂不能分享");
      }
    },
    // 分享详情
    shareDetails(row) {
      let data = {
        fileId: row.id,
      };
      userService.PersonalDocumentsShareDetails(data).then((res) => {
        if (res.code == 200) {
          this.shareTableData = res.rows;
        }
      });
      this.shareDetailShow = true;
    },
    /** 分享回调 */
    shareSet(e) {
      let data = {
        type: "personalSharing",
        fileIds: this.shareID.toString(),
        receiveIds: e.receiveIds.toString(),
        receiveBys: e.receiveBys.toString(),
        operation: e.operation,
        endTime: e.endTime,
        shareTime: e.shareTime,
      };
      userService.personalShareFile(data).then((res) => {
        if (res.code === 200) {
          this.$message.success("分享成功");
          this.$emit("tableDataList", this.leftCheckedNode.id);
          this.$refs.shareView.showShare = false;
        }
      });
    },
    // 分享给我
    shareToMe(row) {
      if (row[0].operation == "2") {
        this.download(row[0]);
      } else {
        this.$message.info("您当前请拥有浏览权限,不可下载!");
      }
    },
    /** 归档对话框：关闭 */
    closeArchive() {
      this.showArchive = false;
      this.archiveForm.archiveTime = [];
      this.archiveForm.archiveId = "";
      this.archiveForm.path = "";
      this.$refs.archiveForm.resetFields();
    },
    // 获取归档配置id
    getArchiveId(id) {
      this.archiveList.filter((item) => {
        if (item.id === id) {
          this.archiveForm.path = item.fullName;
        }
      });
    },
    /** 归档对话框：提交 */
    submitArchive() {
      this.$refs.archiveForm.validate((valid) => {
        if (valid) {
          let data = {
            fileId: this.archiveId.toString(),
            archiveConfigId: this.archiveForm.archiveId,
            startTime: this.timeType==1? '':this.archiveForm.archiveTime[0],
            endTime: this.timeType==1? '':this.archiveForm.archiveTime[1],
            filingMethod: this.archiveForm.func,
          };
          userService.personalArchive(data).then((res) => {
            if (res.code === 200) {
              this.$message.success("归档成功");
              this.$emit("tableDataList", this.leftCheckedNode.id);
            }
          });
          this.closeArchive();
        } else {
          return false;
        }
      });
    },
    // 文件归档弹框-下拉列表
    archiveFile(row) {
      let isChecked = row.every((val, index, arr) => {
        return arr[index].docType == "directory" ? false : true;
      });
      if (isChecked) {
        let filing = row.every((val, index, arr) => {
          return arr[index].filing == "true" ? false : true;
        });
        if (filing) {
          this.archiveForm.person = this.$store.state.user.nickName;
          this.archiveForm.dept = this.userInfo.dept.deptName;
          this.showArchive = true;
          let data = {
            type: 1,
          };
          userService.personalArchiveList(data).then((res) => {
            this.archiveList = res.rows;
          });
          let id = [];
          row.forEach((element) => {
            id.push(element.id);
          });
          this.archiveId = id;
        } else {
          this.$message.warning("不能对已归档的文件继续归档");
        }
      } else {
        this.$message.info("文件夹不能归档");
      }
    },
    // 我的收藏-加入我的收藏
    addCollect(row) {
      this.collectId = row;
    },
    // 移除收藏
    removeCollection(row) {
      let ids = [];
      row.forEach((element) => {
        ids.push(element.id);
      });
      let fileId = ids.toString();

      userService.personalCollectionDelete(fileId).then((res) => {
        if (res.code === 200) {
          this.$message.success("移除成功");
          this.$emit("myCollection");
        }
      });
    },
    // 删除分享
    deleteShare(row) {
      let ids = [];
      row.forEach((element) => {
        ids.push(element.shareId);
      });
      let id = ids.toString();
      userService.personalShareDelete(id).then((res) => {
        if (res.code === 200) {
          this.$message.success("删除分享成功");
          this.$emit("myShare");
        }
      });
    },
    // 回收站还原
    restoreFile(row) {
      let ids = [];
      row.forEach((element) => {
        ids.push(element.docId);
      });
      let docId = ids.toString();
      userService.personalRecycleReduction(docId).then((res) => {
        if (res.code === 200) {
          this.$message.success("还原成功");
          this.$emit("recycleBin");
          this.$emit("getTree");
        }
      });
    },
    // 回收站彻底删除
    deleteFile2(row) {
      let ids = [];
      row.forEach((element) => {
        ids.push(element.id);
      });
      let id = ids.toString();
      userService.personalRecycleClear(id).then((res) => {
        if (res.code === 200) {
          this.$message.success("彻底删除成功");
          this.$emit("recycleBin");
        }
      });
    },
    // 归档成果-签入-签出
    /** 签出 */
    checkout(row) {
      this.checkdata = row;
      // 0签出-上锁 表示可以进行签入      1签入-解锁 表示可以签出
      if (row) {
        if (row.locked == 1 || row.locked == null) {
          if (this.socket.readyState === 1) {
            // 签出标识
            const checkState = "checkout";
            // 主键id
            // let id = row.id
            // 获取token值
            let tokenId = this.getCookie("Admin-Token");
            // 获取签出文件名
            let fileName = row.resultName;
            // 获取用户Id
            let userId = this.$store.state.user.userId;
            let data = [row.id, userId, tokenId, fileName];
            let id = checkState + ":" + data.toString();
            // 发送webSocket请求
            this.socket.send(JSON.stringify(id));
          } else if (this.socket.readyState === 3) {
            this.checkOutIn = true;
          }
        } else if (row.locked == 0) {
          // 0签出-上锁 表示可以进行签入
          if (this.socket.readyState === 1) {
            const h = this.$createElement;
            const objstatusTr = h(
              "input",
              {
                style: { margin: "10px 10px" },
                attrs: {
                  name: "statusR",
                  type: "radio",
                  value: 0,
                  checked: this.statusclickTr,
                },
                on: {
                  click: function (event) {
                    this.flag = event.target.value;
                  }.bind(this),
                },
              },
              ""
            );
            const objstatusFa = h(
              "input",
              {
                style: { margin: "10px 10px" },
                attrs: {
                  name: "statusR",
                  type: "radio",
                  value: 1,
                  checked: !this.statusclickTr,
                },
                on: {
                  click: function (event) {
                    this.flag = event.target.value;
                  }.bind(this),
                },
              },
              ""
            );
            this.$msgbox({
              title: "版本选择",
              closeOnClickModal: false,
              message: h("p", null, [objstatusTr, "版本", objstatusFa, "版次"]),
            })
              .then((action) => {
                if (action === "confirm") {
                  // 签出标识
                  const checkState = "checkin";
                  // 获取token值
                  let tokenId = this.getCookie("Admin-Token");
                  // 获取签出文件名
                  let fileName = row.resultName;
                  // 获取用户Id
                  let userId = this.$store.state.user.userId;
                  // 获取版本(0)或版次(1)
                  let flag = this.flag;
                  let data = [row.id, tokenId, this.flag, fileName, userId];
                  let id = checkState + ":" + data.toString();
                  // checkId = checkState + ":" + data.toString()
                  // this.checkInId = id;
                  // 发送webSocket请求

                  this.socket.send(JSON.stringify(id));
                  return id;
                }
              })
              .catch((e) => {});
          } else if (this.socket.readyState === 3) {
            this.checkOutIn = true;
          }
        }
      } else {
        this.$message.warning("暂不支持多文件签出");
      }
    },
    // 触发websocket
    oneTouchStart() {
      location.href = "FileAssistant://";
      this.init();

      this.errorSetInerval = setInterval(() => {
        this.init();
      }, 2000);
    },
    // 下载文件助手
    dowloadClick() {
      location.href = this.initCurrentIP + "/FileAssistant/FileAssistant.exe";
    },
    // 最近浏览-添加收藏
    addCollect2(row) {
      let ids = [];
      row.forEach((element) => {
        ids.push(element.docId);
      });
      if (row.length == 1 && row[0].flag == "0") {
        userService
          .PersonalDocumentsRecentBrowsingFavorites(ids)
          .then((res) => {
            if (res.code === 200) {
              this.$message.success("收藏成功");
              this.$emit("recentView");
            }
          });
      } else if (row.length == 1 && row[0].flag == "1") {
        let id = row[0].docId;
        userService
          .PersonalDocumentsRecentBrowsingRemoveFavorites(id)
          .then((res) => {
            if (res.code === 200) {
              this.$message.warning("取消收藏");
              this.$emit("recentView");
            }
          });
      } else {
        userService
          .PersonalDocumentsRecentBrowsingFavorites(ids)
          .then((res) => {
            if (res.code === 200) {
              this.$message.success("收藏成功");
              this.$emit("recentView");
            }
          });
      }
    },
    // 获取本地token值
    getCookie(name) {
      let strCookie = document.cookie;
      let arrCookie = strCookie.split(";");
      for (let i = 0; i < arrCookie.length; i++) {
        let arr = arrCookie[i].split("=");
        if (arr[0].replace(/(^\s*)|(\s*$)/g, "") == name) {
          return arr[1];
        }
      }
      return "";
    },
    // webSocket创建
    init() {
      if (typeof WebSocket === "undefined") {
        alert("您的浏览器不支持socket");
      } else {
        this.socket = new WebSocket(this.websocketUrl.socketRoot); // 实例化socket
        this.socket.onopen = this.open; // 监听socket连接
        this.socket.onerror = this.error; // 监听socket错误信息
        this.socket.onmessage = this.getMessage; // 监听socket消息
        this.socket.onclose = this.close; // 关闭socket消息
      }
      // }
    },
    open(row) {
      if (this.checkOutIn) {
        if (this.socket.readyState === 1) {
          let id = this.checkout(this.checkdata);
        }
      }

      clearInterval(this.errorSetInerval);
    },
    error() {},
    async getMessage(msg) {
      let data = msg.data.split(":");

      let status = "";
      if (data[0] == "checkout status" && data[1] == "1") {
        this.$message.success("签出成功");
        this.$emit("archive");
      } else if (data[0] == "checkout status" && data[1] == "0") {
        this.$message.error("签出失败");
        clearInterval(this.errorSetInerval);
      } else if (data[0] == "checkin status" && data[1] == "1") {
        this.$message.success("签入成功");
        this.$emit("archive");
      } else if (data[0] == "checkin status" && data[1] == "0") {
        this.$message.error("签入失败");
        clearInterval(this.errorSetInerval);
      }

      return status;
    },
    // ref
    close() {
      //
    },
    // 刷新列表
    getListChild() {
      this.$emit("getList");
    },
  },
};
</script>

<style lang="scss" scoped>
.tree-wrap {
  height: 360px;
  overflow-y: auto;
}
::v-deep .el-dialog__body {
  padding: 30px 20px 0;
}
.checkOutIn {
  display: flex;
  justify-content: center;
  .check {
    width: 225px;
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    // background-color: pink;
    div {
      margin: 10px;
    }
  }
}
.dowload {
  position: absolute;
  top: 7%;
  right: 42%;
}
</style>
