<template>
  <div class="wrap">
    <div id="content-left" class="content-left" ref="leftDom" style="width: 225px">
      <div class="handle-tools">
        <div @click="newFolder">
          <el-tooltip effect="dark" content="新建文件夹" placement="top">
            <svg-icon icon-class="材料新建icon" class="svgIcon"/>
          </el-tooltip>
        </div>
        <div @click="editFolder">
          <el-tooltip effect="dark" content="修改" placement="top">
            <svg-icon icon-class="材料重命名icon" class="svgIcon"/>
          </el-tooltip>
        </div>
        <div @click="deleteFile(true)">
          <el-tooltip effect="dark" content="删除" placement="top">
            <svg-icon icon-class="材料删除icon" class="svgIcon"/>
          </el-tooltip>
        </div>
        <div @click="handover">
          <el-tooltip effect="dark" content="一键移交" placement="top">
            <svg-icon icon-class="材料移交icon" class="svgIcon"/>
          </el-tooltip>
        </div>
      </div>
      <div class="tree" @click="cancleAllClickNode" :style="
          isFlip ? 'height:calc(100% - 75px);' : 'height:calc(100% - 50%);'
        ">
        <el-tree ref="tree" node-key="id" highlight-current :data="treeData" :props="defaultProps"
                 :default-expanded-keys="expanKeys" :expand-on-click-node="false" @node-click="handleNodeClick">
					<span class="tree-node" slot-scope="{ node }" :title="node.label">
						<img src="@/assets/docCenter/folder.png" class="tree-node__icon"/>
						<span>{{ node.label }}</span>
            <!-- <span>{{ "(" + data.count + ")" }}</span> -->
					</span>
        </el-tree>
      </div>
      <div class="flip border-top" @click="isFlip = !isFlip">
        <i class="el-icon-caret-bottom" v-show="!isFlip"></i>
        <i class="el-icon-caret-top" v-show="isFlip"></i>
      </div>
      <div v-show="!isFlip"
           :style="isFlip ? 'height: 350px;' : 'height:41%;overflow-y: scroll;padding-bottom: 20px;'">
        <div class="type-options border-top" :class="tableStyle == 1 ? 'actBG' : ''"
             @click="changeTableStyle(1)">
          <i class="el-icon-star-on pr5"></i>我的收藏
        </div>
        <div class="type-options" :class="tableStyle == 2 ? 'actBG' : ''" @click="changeTableStyle(2)">
          <i class="el-icon-share pr5"></i>我的分享
        </div>
        <div class="type-options" :class="tableStyle == 3 ? 'actBG' : ''" @click="changeTableStyle(3)">
          <i class="el-icon-paperclip pr5"></i>分享给我
        </div>
        <div class="type-options" :class="tableStyle == 4 ? 'actBG' : ''" @click="changeTableStyle(4)">
          <i class="el-icon-delete pr5"></i>回收站
        </div>
        <div class="type-options border-top" :class="tableStyle == 5 ? 'actBG' : ''"
             @click="changeTableStyle(5)">
          <i class="el-icon-receiving pr5"></i>归档成果
        </div>
        <div class="type-options" :class="tableStyle == 6 ? 'actBG' : ''" @click="changeTableStyle(6)">
          <i class="el-icon-time pr5"></i>最近浏览
        </div>
      </div>
      <div class="move-line" ref="moveDom"></div>
    </div>
    <div class="map-toggle-out">
      <i id="inOutButton" :class="typeName ? 'el-icon-d-arrow-left' : 'el-icon-d-arrow-right'"
         @click="toggle"></i>
    </div>
    <div class="content-right">
      <div class="top">
        <tools-line :btns="btnList[tableStyle]" :tabs="tabs" :sort-list="sortList" :show-btns="showBtns"
                    :show-tools="showTools" :show-upload="!isRecycle && tableStyle === 0" :crumbs-data="crumbsData"
                    :collectEvent="collectEvent" @btnClick="btnClick" @uploadClick="uploadFile" @radioClick="radioClick"
                    @crumbsBack="crumbsBack" @change="sortByChange"></tools-line>
      </div>
      <div class="main" style="position: relative">
        <file-table style="position: absolute; width: 100%" ref="fileTable" :btns="btnList[tableStyle]"
                    :isCardPlay="isCardPlay" :showTools="showTools" :table-data="tableData" :collectEvent="collectEvent"
                    :permission="permission" :archiveImg="archiveImg" :isCollect="isCollect" @setTreeNode="setTreeNode"
                    @tableDataList="tableDataList" @previewFile="previewFile" @selectionChange="selectionChange"
                    @goInFolder="goInFolder" @handleItemClick="handleItemClick(arguments)"></file-table>
        <pagination style="position: absolute; right: 0; bottom: 0" v-show="total > 0" :total="total"
                    :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList"/>
      </div>
    </div>
    <table-handle ref="tableHandle" :tableStyle="tableStyle" :treeData="treeData" :leftCheckedNode="leftCheckedNode"
                  :rightCheckedList="rightCheckedList" @archive="archive" @getList="getList" @getTree="getTree"
                  @recentView="recentView" @myCollection="myCollection" @myShare="myShare" @recycleBin="recycleBin"
                  @tableDataList="tableDataList"></table-handle>
    <tree-handle ref="treeHandle" :leftCheckedNodeId="leftCheckedNodeId" :leftCheckedNode="leftCheckedNode"
                 @getTree="getTree" @getList="getList" @tableDataList="tableDataList"></tree-handle>
  </div>
</template>
<script>
/**
 * 表格类型，不同类型表示不同的按钮组
 * 0——我的文件——移动0、下载1、删除2、分享3、归档4、详细信息5
 * 1——我的收藏——加入我的文件(移动)0、下载1、移除收藏2、详细信息3
 * 2——我的分享——下载0、删除分享1
 * 3——分享给我——下载0
 * 4——回收站——还原0、彻底删除1
 * 5——归档成果——签入0、签出1
 * 6——最近浏览——收藏0、下载1
 */
import {
  getUserProfile
} from "@/api/system/user";
import userService from "@/api/techdocmanage/docCenter/user";
import toolsLine from "../toolsLine.vue";
import fileTable from "./fileTable.vue";
import tableHandle from "./tableHandle.vue";
import treeHandle from "./treeHandle.vue";

export default {
  name: "PersonalDocuments",
  components: {
    toolsLine,
    fileTable,
    tableHandle,
    treeHandle,
  },
  data() {
    return {
      typeName: true,
      leftDom: null,
      clientStartX: 0,
      isCollect: 0,
      defaultProps: {
        children: "children",
        label: "userDocName",
        isLeaf: "folderFlag",
      },
      isFlip: false,
      treeData: [],
      expanKeys: [],
      total: 0,
      total2: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      queryParams2: {
        pageNum: 1,
        pageSize: 10,
      },
      leftCheckedNodeId: null,
      leftCheckedNode: null,
      tableStyle: 0,
      searchInputVal: '',
      btnList: [
        [{
          label: "详细信息",
          type: null,
          value: 5,
          src: require("@/assets/docCenter/detail_info.png"),
          icon: "el-icon-info",
        },
          {
            label: "移动",
            type: "primary",
            value: 0,
            src: require("@/assets/docCenter/move.png"),
            icon: "el-icon-rank",
          },
          {
            label: "下载",
            type: "info",
            value: 1,
            src: require("@/assets/docCenter/download.png"),
            icon: "el-icon-download",
          },
          {
            label: "删除",
            type: "danger",
            value: 2,
            src: require("@/assets/docCenter/delete.png"),
            icon: "el-icon-delete",
          },
          {
            label: "分享",
            type: "warning",
            value: 3,
            src: require("@/assets/docCenter/share.png"),
            icon: "el-icon-share",
            disabled: false,
          },
          {
            label: "归档",
            type: "success",
            value: 4,
            src: require("@/assets/docCenter/archive.png"),
            icon: "el-icon-s-management",
          },

          {
            label: "标签设置",
            type: "info",
            value: 6,
            src: require("@/assets/docCenter/detail.png"),
            icon: "el-icon-setting",
          },
          {
            label: "文件对话",
            type: "primary",
            value: 99,
            src: require("@/assets/docCenter/file.png"),
            icon: "el-icon-info",
          },
        ],
        [{
          label: "加入我的文件",
          type: "primary",
          value: 0,
          src: require("@/assets/docCenter/move.png"),
          src2: require("@/assets/docCenter/move.png"),
          icon: "el-icon-folder-opened",
        },
          {
            label: "下载",
            type: "info",
            value: 1,
            src: require("@/assets/docCenter/download.png"),
            src2: require("@/assets/docCenter/download.png"),
            icon: "el-icon-download",
          },
          {
            label: "移除收藏",
            type: "danger",
            value: 2,
            src: require("@/assets/docCenter/collect_off.png"),
            src2: require("@/assets/docCenter/collect_off.png"),
            icon: "el-icon-s-release",
          },
        ],
        [{
          label: "分享详情",
          type: "success",
          value: 2,
          src: require("@/assets/docCenter/share.png"),
          src2: require("@/assets/docCenter/share.png"),
          icon: "el-icon-postcard",
        },
          {
            label: "下载",
            type: "info",
            value: 0,
            src: require("@/assets/docCenter/download.png"),
            src2: require("@/assets/docCenter/download.png"),
            icon: "el-icon-download",
          },
          {
            label: "删除分享",
            type: "danger",
            value: 1,
            src: require("@/assets/docCenter/share_off.png"),
            src2: require("@/assets/docCenter/share_off.png"),
            icon: "el-icon-document-delete",
          },
        ],
        [{
          label: "下载",
          label2: "下载",
          type: "info",
          value: 0,
          src: require("@/assets/docCenter/download.png"),
          icon: "el-icon-download",
        },],
        [{
          label: "还原",
          type: "primary",
          value: 0,
          src: require("@/assets/docCenter/restore.png"),
          icon: "el-icon-refresh-left",
        },
          {
            label: "彻底删除",
            type: "danger",
            value: 1,
            src: require("@/assets/docCenter/delete.png"),
            icon: "el-icon-delete",
          },
        ],
        [{
          label: "签出",
          type: "primary",
          value: 1,
          src: require("@/assets/docCenter/checkout.png"),
          src2: require("@/assets/docCenter/checkin.png"),
          icon: "el-icon-d-arrow-right",
          disabled: true,
        },],
        [{
          label: "收藏",
          type: "warning",
          value: 0,
          src: require("@/assets/docCenter/collect.png"),
          src2: require("@/assets/docCenter/collect_full.png"),
          icon: "el-icon-star-off",
        },
          {
            label: "下载",
            type: "info",
            value: 1,
            src: require("@/assets/docCenter/download.png"),
            src2: require("@/assets/docCenter/download.png"),
            icon: "el-icon-download",
          },
        ],
      ],
      tabList: [
        [{
          label: "未过期",
          value: "1"
        },
          {
            label: "已过期",
            value: "0"
          },
        ],
        [{
          label: "归档成果",
          value: -1
        },
          {
            label: "可签出",
            value: 1
          },
          {
            label: "已签出",
            value: 0
          },
        ],
        [{
          label: "个人",
          value: "personalSharing"
        },
          {
            label: "知识库",
            value: "businessSharing"
          },
        ],
        [{
          label: "个人",
          value: "personalSharing"
        }],
      ],
      tabs: [],
      sortList: [{
        label: "名称",
        value: 0
      },
        {
          label: "大小",
          value: 1
        },
        {
          label: "上传人",
          value: 2
        },
        {
          label: "上传时间",
          value: 3
        },
      ],
      crumbsData: [],
      showBtns: false,
      showTools: true,
      isCardPlay: true,
      isRecycle: false,
      tableData: [],
      // 右侧列表-选择数组
      rightCheckedList: [],
      // 分享给我-类型（0已过期、1未过期）
      shareType: "1",
      // 分享状态
      shareStatus: {
        // 分页状态-我的收藏
        collectType: false,
        // 分页状态-我的分享
        myShareType: false,
        // 分页状态-分享给我
        shareToMeType: false,
        // 分页状态-回收站
        recycleType: false,
        // 分页状态-归档成果
        archiveType: false,
        // 分页状态-最近浏览
        latelyType: false,
      },
      // 收藏事件
      collectEvent: "",
      // 归档状态图片
      archiveImg: false,
      // 我的收藏-类型
      myShareType: "personalSharing",
      // 归档-类型
      archiveType: -1,
      // 权限标识
      permission: false,
    };
  },
  watch: {
    // shareType: function(val, oldVal) {
    // 	if (val !== oldVal) {
    // 		this.shareToMe();
    // 	}
    // },
    // myShareType: function(val, oldVal) {
    // 	if (val !== oldVal) {
    // 		this.myShare();
    // 	}
    // },
    $route: function (val, oldVal) {
      //最近浏览 路由跳转
      if (val.query.fn == 6) {
        this.recentView();
        this.changeTableStyle(val.query.fn);
      }
      //分享给我 路由跳转
      if (val.query.fn == 3) {
        this.shareToMe();
        this.changeTableStyle(val.query.fn);
      }
      // 快速访问 路由跳转
      if (val.query.fn == 1) {
      }
    },
  },
  created() {
    // 控制是否屏蔽全局console.log 日志；isDebug设为false即可屏蔽
    // const isDebug = false;
    // console.log = (function (oldLogFunc) {
    //   return function () {
    //     if (isDebug) {
    //       oldLogFunc.apply(this, arguments);
    //     }
    //   };
    // })(console.log);;
  },
  mounted() {
    $("#inOutButton").click(function () {
      $("#content-left").animate({
        width: "toggle"
      }, 350);
    });
    if (this.$route.query.fn == 6) {
      this.recentView();
      this.changeTableStyle(this.$route.query.fn);
    }
    //分享给我 路由跳转
    if (this.$route.query.fn == 3) {
      this.shareToMe();
      this.changeTableStyle(this.$route.query.fn);
    }
    // 快速访问 路由跳转
    if (this.$route.query.fn == 1) {
    }
    getUserProfile().then((response) => {
      this.userInfo = response.data;
    });
    /** 左侧树拖拽 */
    this.leftDom = this.$refs.leftDom;
    let moveDom = this.$refs.moveDom;
    moveDom.onmousedown = (e) => {
      this.clientStartX = e.clientX;
      e.preventDefault();

      document.onmousemove = (e) => {
        this.moveHandle(e.clientX, this.leftDom);
      };

      document.onmouseup = (e) => {
        document.onmouseup = null;
        document.onmousemove = null;
      };
    };
    /** 监听可视区变化 */
    if (document.body.clientWidth < 1200) {
      this.showTools = false;
    }
    window.onresize = () => {
      return (() => {
        if (document.body.clientWidth < 1200) {
          this.showTools = false;
        } else {
          this.showTools = true;
        }
      })();
    };
    /** 初始化树结构 */
    this.getTree();
  },
  methods: {
    toggle() {
      this.typeName = !this.typeName;
    },
    /** 表格内按钮事件 */
    handleItemClick(e) {
      this.$refs.tableHandle.trigger(e);
    },
    /** 菜单栏按钮点击事件 */
    btnClick(e) {
      let rows = this.$refs.fileTable.btnClick();
      this.$refs.tableHandle.trigger([rows, e]);
    },
    /** 表格类型变换(左菜单点击事件) */
    changeTableStyle(e) {
      this.$refs.tree.setCurrentKey(null);

      this.collectEvent = e;
      this.showBtns = false;
      this.isRecycle = (e === 4); // 回收站模式
      let querSearchParams = ['userDocName', 'resultName']
      Object.keys(this.queryParams).forEach(item => {
        if (querSearchParams.indexOf(item) != -1) {
          delete this.queryParams[item]
        }
      });
      if (e !== 0) {
        this.crumbsData.splice(0, this.crumbsData.length);
      }
      if (e === 1) {
        this.tabs = [];
        this.archiveImg = false;
        this.leftCheckedNodeId = null;
        this.shareStatus.collectType = true;
        this.shareStatus.myShareType = false;
        this.shareStatus.shareToMeType = false;
        this.shareStatus.recycleType = false;
        this.shareStatus.archiveType = false;
        this.shareStatus.latelyType = false;
        this.isCollect = 1;
        this.queryParams.resultName = this.searchInputVal;
      } else if (e === 2) {
        // this.myShare();
        //
        if (this.userInfo.administrator) {
          this.tabs = this.tabList[2];
        } else {
          this.tabs = this.tabList[3];
        }
        this.archiveImg = false;
        this.leftCheckedNodeId = null;
        this.shareStatus.collectType = false;
        this.shareStatus.myShareType = true;
        this.shareStatus.shareToMeType = false;
        this.shareStatus.recycleType = false;
        this.shareStatus.archiveType = false;
        this.shareStatus.latelyType = false;
        this.isCollect = 0;
      } else if (e === 3) {
        // this.shareToMe();
        this.archiveImg = false;
        this.tabs = this.tabList[0];
        this.leftCheckedNodeId = null;
        this.shareStatus.collectType = false;
        this.shareStatus.myShareType = false;
        this.shareStatus.shareToMeType = true;
        this.shareStatus.recycleType = false;
        this.shareStatus.archiveType = false;
        this.shareStatus.latelyType = false;
        this.isCollect = 0;
      } else if (e === 4) {
        // this.recycleBin();
        this.tabs = [];
        this.archiveImg = false;
        this.leftCheckedNodeId = null;
        this.shareStatus.collectType = false;
        this.shareStatus.myShareType = false;
        this.shareStatus.shareToMeType = false;
        this.shareStatus.recycleType = true;
        this.shareStatus.archiveType = false;
        this.shareStatus.latelyType = false;
        this.isCollect = 0;
      } else if (e === 5) {
        // this.archive();
        this.archiveImg = true;
        this.leftCheckedNodeId = null;
        this.shareStatus.collectType = false;
        this.shareStatus.myShareType = false;
        this.shareStatus.shareToMeType = false;
        this.shareStatus.recycleType = false;
        this.shareStatus.archiveType = true;
        this.shareStatus.latelyType = false;
        this.tabs = this.tabList[1];
        this.isCollect = 0;
      } else if (e === 6) {
        this.archiveImg = false;
        this.leftCheckedNodeId = null;
        this.shareStatus.collectType = false;
        this.shareStatus.myShareType = false;
        this.shareStatus.shareToMeType = false;
        this.shareStatus.recycleType = false;
        this.shareStatus.archiveType = false;
        this.shareStatus.latelyType = true;
        this.tabs = [];
        this.isCollect = 2;
      }
      this.tableStyle = e;
      this.getList();
    },
    // 我的收藏
    myCollection() {
      // this.collectType = true;

      delete this.queryParams.type;
      delete this.queryParams.locked;
      userService.personalCollection(this.queryParams).then((res) => {
        this.tableData = res.rows;
        this.total = res.total;
      });
    },
    // 我的分享
    myShare(e) {
      delete this.queryParams.type;
      delete this.queryParams.locked;
      this.queryParams.type = this.myShareType;
      userService.personalShare(this.queryParams).then((res) => {
        this.tableData = res.rows;
        this.total = res.total;
      });
    },
    // 分享给我
    shareToMe() {
      if (typeof this.shareType === "string") {
        let data = {
          type: this.shareType,
        };

        this.queryParams.type = this.shareType;
        // delete this.queryParams.type;
        delete this.queryParams.locked;

        userService.personalShareToMe(this.queryParams).then((res) => {
          this.tableData = res.rows;
          this.total = res.total;
        });
      }
    },
    // 回收站
    recycleBin() {
      delete this.queryParams.type;
      delete this.queryParams.locked;
      userService.personalRecycleList(this.queryParams).then((res) => {
        this.tableData = res.rows;
        this.total = res.total;
      });
    },
    // 归档成果
    archive() {
      // 可签出0解锁  已签出1上锁   -1 归档成果

      if (this.archiveType === -1) {
        delete this.queryParams.type;
        delete this.queryParams.locked;
        userService.personalArchiveResult(this.queryParams).then((res) => {
          this.tableData = res.rows;
          this.total = res.total;
        });
      } else {
        let data = {
          locked: this.archiveType,
        };

        this.queryParams.locked = this.archiveType;

        userService.personalArchiveResult(this.queryParams).then((res) => {
          this.tableData = res.rows;
          this.total = res.total;
        });
      }
    },
    // 最近浏览
    recentView() {
      let data = {
        startTime: 1,
        endTime: 2,
      };
      delete this.queryParams.type;
      delete this.queryParams.locked;
      userService.personalRecent(this.queryParams).then((res) => {
        this.total = res.total;
        this.tableData = res.rows;
      });
    },
    /** 列表筛选条件变化触发事件 */
    sortByChange(e) {
      this.searchInputVal = e.inputValue;
      console.log(e, 'eeeeeee')
      if (e.flag === 1) {
        this.queryParams.resultName = e.inputValue;
        this.checkSort(e);
        this.myCollection();
      } else if (e.flag === 2) {
        this.queryParams.userDocParent = e.inputValue;
        this.myShareType = e.tabValue;
        this.checkSort(e);
        this.myShare();
      } else if (e.flag === 3) {
        this.queryParams.resultName = e.inputValue;
        this.shareType = e.tabValue;
        this.checkSort(e);
        this.shareToMe();
      } else if (e.flag === 4) {
        this.queryParams.userDocName = e.inputValue;
        this.checkSort(e);
        this.recycleBin();
      } else if (e.flag === 5) {
        this.queryParams.resultName = e.inputValue;
        this.archiveType = e.tabValue;
        this.checkSort(e);
        this.archive();
      } else if (e.flag === 6) {
        this.queryParams.docName = e.inputValue;
        this.checkSort(e);
        this.recentView();
      } else {
        if (!this.queryParams.userDocParent) return
        this.queryParams.order = e.sortDesc ? "asc" : "desc";
        this.queryParams.userDocName = e.inputValue;
        let sort;
        if (e.sortBy === 0) {
          this.queryParams.sort = "doc_document_user.user_doc_name";
        } else if (e.sortBy === 1) {
          this.queryParams.sort = "doc_document.doc_size";
        } else if (e.sortBy === 2) {
          this.queryParams.sort = "doc_document_user.create_id";
        } else if (e.sortBy === 3) {
          this.queryParams.sort = "doc_document_user.create_time";
        }

        userService.personalFileList(this.queryParams).then((res) => {
          if (res.code === 200) {
            this.tableData = res.rows;
            this.total = res.total;
            // this.$message.success("排序成功");
          }
        });
      }
    },
    checkSort(e) {
      this.queryParams.order = e.sortDesc ? "asc" : "desc";
      // this.queryParams.resultName = e.inputValue;
      let sort;
      switch (e.flag) {
        case 1:
          if (e.sortBy === 0) {
            this.queryParams.sort = "doc_document_result.result_name";
          } else if (e.sortBy === 1) {
            this.queryParams.sort = "doc_document.doc_size";
          } else if (e.sortBy === 2) {
            this.queryParams.sort = "doc_document_result.create_id";
          } else if (e.sortBy === 3) {
            this.queryParams.sort = "doc_document_collect.collect_time";
          }
          break;
        case 2:
          if (e.sortBy === 0) {
            this.queryParams.sort = "";
          } else if (e.sortBy === 1) {
            this.queryParams.sort = "";
          } else if (e.sortBy === 2) {
            this.queryParams.sort = "";
          } else if (e.sortBy === 3) {
            this.queryParams.sort = "";
          }
          break;
        case 3:
          if (e.sortBy === 0) {
            this.queryParams.sort = "a.result_name";
          } else if (e.sortBy === 1) {
            this.queryParams.sort = "a.doc_size";
          } else if (e.sortBy === 2) {
            this.queryParams.sort = "a.share_id";
          } else if (e.sortBy === 3) {
            this.queryParams.sort = "a.end_time";
          }
          break;
        case 4:
          if (e.sortBy === 0) {
            this.queryParams.sort = "doc_document_user.user_doc_name";
          } else if (e.sortBy === 1) {
            this.queryParams.sort = "doc_document.doc_size";
          } else if (e.sortBy === 2) {
            this.queryParams.sort = "doc_document.delete_id";
          } else if (e.sortBy === 3) {
            this.queryParams.sort = "doc_document.delete_time";
          }
          break;
        case 5:
          if (e.sortBy === 0) {
            this.queryParams.sort = "doc_document_result.result_name";
          } else if (e.sortBy === 1) {
            this.queryParams.sort = "doc_document.doc_size";
          } else if (e.sortBy === 2) {
            this.queryParams.sort = "doc_document_result.create_id";
          } else if (e.sortBy === 3) {
            this.queryParams.sort = "doc_document_result.create_time";
          }
          break;
        case 6:
          if (e.sortBy === 0) {
            this.queryParams.sort = "doc_activity_log.doc_name";
          } else if (e.sortBy === 1) {
            this.queryParams.sort = "doc_document.doc_size";
          } else if (e.sortBy === 2) {
            this.queryParams.sort = "doc_activity_log.operate_id";
          } else if (e.sortBy === 3) {
            this.queryParams.sort = "doc_activity_log.operate_time";
          }
          break;
      }
    },
    /** 列表多选框选择事件 */
    selectionChange(list) {
      this.rightCheckedList = list;
      if (list && list.length > 0) {
        this.showBtns = true;
      } else {
        this.showBtns = false;
      }
    },
    /** 表格数据展示形式选择 */
    radioClick(e) {
      this.isCardPlay = e === "摘要" ? true : false;
    },
    /** 新建文件夹 */
    newFolder() {
      this.$refs.treeHandle.addNewFolder();
    },
    /** 修改文件夹 */
    editFolder() {
      let clickNode = this.$refs.tree.getCurrentNode();
      this.$refs.treeHandle.editFolderFuc(clickNode);
    },
    /** 删除文件夹 */
    deleteFile() {
      let clickNode = this.$refs.tree.getCurrentNode();

      if (!clickNode) {
        this.$message.warning("请选择文件");
        return;
      }
      if (clickNode.type == "business" || clickNode.id == "file") {
        this.$message.warning("不可删除公共文件夹");
        return;
      }
      this.$confirm(
        "删除文件夹会使该文件夹下所有文件都被删除，是否确定删除？",
        "提示", {
          confirmButtonText: "删除",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        if (clickNode.docType === "directory") {
          userService.personalFileDelete(clickNode.docId).then((res) => {
            this.$message.success("文件夹删除成功");
            this.leftCheckedNodeId = null;
            this.getTree();
          });
        }
      });
    },
    /** 文件上传 */
    uploadFile() {
      if (!this.leftCheckedNodeId) {
        return this.$message.info("请选择文件夹进行上传");
      }

      // 检查当前选中的节点是否可以上传文件
      if (this.leftCheckedNode && this.leftCheckedNode.docType === "directory") {
        this.$refs.treeHandle.showUpload = true;
      } else {
        this.$message.info("请选择文件夹进行上传");
      }
    },
    /** 一键移交 */
    handover() {
      this.$refs.treeHandle.showHandover = true;
    },
    /** 面包屑-返回事件 */
    crumbsBack(e) {
      if (e) {
        this.tableDataList(e.parentId); //进入文件夹
        this.crumbsData = this.getParent({
          id: e.parentId
        }); //面包屑重载
        if (this.crumbsData.length === 0) {
          this.$refs.tree.setCurrentKey(null); //左边树节点选中
        } else {
          this.$refs.tree.setCurrentKey(e.parentId); //左边树节点选中
        }
      }
    },
    /** 面包屑-递归查找父结构 */
    getParent(data) {
      const treeFindPath = (tree, subNode, path = []) => {
        if (!tree) return [];
        for (const data of tree) {
          path.push({
            name: data.userDocName,
            id: data.id,
            parentId: data.userDocParent,
          });
          if (data.id === subNode.id) return path;
          if (data.children) {
            const findChildren = treeFindPath(data.children, subNode, path);
            if (findChildren.length) return findChildren;
          }
          path.pop();
        }
        return [];
      };
      let result = treeFindPath(this.treeData, data);
      //
      return result;
    },
    /** 左侧树点击事件 */
    handleNodeClick(data, node) {
      delete this.queryParams.sort;
      this.archiveImg = false;
      this.isCollect = false;
      this.isRecycle = false; // 点击树节点时退出回收站模式

      if (this.tableStyle != 0) {
        this.tableStyle = 0;
        this.tabs = [];
      }
      this.collectEvent = false;
      if (data.folderFlag == 0) {
        this.getList(data.parentId);
        return;
      }
      this.leftCheckedNodeId = data.id;
      this.leftCheckedNode = data;
      this.expanKeys = [data.id]; //左边树节点展开
      this.queryParams.pageNum = 1;
      this.tableDataList(data.id);
      this.crumbsData = this.getParent(data);
    },
    /** 点击空白处取消树节点选择 */
    cancleAllClickNode() {
      // this.getList(0)
      this.archiveImg = false;
      this.leftCheckedNodeId = null;
      this.$refs.tree.setCurrentKey(null);
      // if (this.tableStyle != 0) {
      //   this.tableStyle = 0;
      //   this.tabs = [];
      // }
    },
    /** 右侧文件夹点击事件 */
    goInFolder(data) {
      this.getList(data.id); //进入文件夹
      this.expanKeys = [data.id]; //左边树节点展开
      this.$refs.tree.setCurrentKey(data.id); //左边树节点选中
      this.crumbsData = this.getParent(data); //面包屑重载
    },
    // 右侧文件点击预览
    previewFile(data) {
      this.$router.push({
        name: "previewPdf",
      });
      // 去掉pdf.js初始关键字搜索
      this.$store.commit("common/PDFDOCKEYWORD", null);
    },
    /** 获取左侧树数据 */
    getTree() {
      userService.personalTreeList().then((res) => {
        this.treeData = res.data;
      });
    },
    /** 获取表格数据 */
    getList(id) {
      if (id && id.limit) {
        //分页
        this.queryParams.pageNum = id.page;
        this.queryParams.pageSize = id.limit;
        // this.queryParams.userDocParent = this.leftCheckedNodeId;
      } else if (id) {
        //常规
        this.queryParams.pageNum = 1;
      } else {
        //条件查询
        this.queryParams.pageNum = 1;
        // this.queryParams.userDocParent = this.leftCheckedNodeId;
      }
      if (this.leftCheckedNodeId) {
        delete this.queryParams.sort;
        this.tableDataList(this.leftCheckedNodeId);
      } else if (this.shareStatus.collectType) {
        delete this.queryParams.sort;
        this.myCollection();
      } else if (this.shareStatus.myShareType) {
        this.myShare();
      } else if (this.shareStatus.shareToMeType) {
        this.shareToMe();
      } else if (this.shareStatus.recycleType) {
        delete this.queryParams.sort;
        this.recycleBin();
      } else if (this.shareStatus.archiveType) {
        delete this.queryParams.sort;
        this.archive();
      } else if (this.shareStatus.latelyType) {
        delete this.queryParams.sort;
        this.recentView();
      }
    },
    // 刷新列表数据
    tableDataList(id) {
      this.queryParams.userDocParent = id;
      userService.personalFileList(this.queryParams).then((res) => {
        this.total = res.total;
        this.tableData = res.rows;
      });
    },
    // 双击文件夹选中左侧节点
    setTreeNode(data) {
      this.$refs.tree.setCurrentKey(data.id); //左边树节点选中
      this.crumbsData = this.getParent(data);
    },
    /** 调整左侧div宽度 */
    moveHandle(nowClientX, leftDom) {
      let computedX = nowClientX - this.clientStartX;
      let leftBoxWidth = parseInt(leftDom.style.width);
      let changeWidth = leftBoxWidth + computedX;
      if (changeWidth < 225) {
        changeWidth = 225;
      }
      if (changeWidth > 400) {
        changeWidth = 400;
      }
      leftDom.style.width = changeWidth + "px";
      this.clientStartX = nowClientX;
    },
  },
};
</script>
<style lang="scss" scoped>
.wrap {
  display: flex;
  // height: calc(100% - 20px);
  height: calc(100vh - 93px);
  background-color: #fff;
  padding: 20px;

  .content-left {
    padding-right: 16px;
    position: relative;

    .handle-tools {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      align-items: center;

      .svgIcon {
        cursor: pointer;
        width: 32px;
        height: 32px;
      }
    }

    .tree {
      // height: calc(100% - 375px);
      overflow-y: auto;

      .tree-node {
        display: flex;
        align-items: center;

        &__icon {
          width: 16px;
          margin-right: 4px;
          vertical-align: middle;
        }
      }
    }

    .flip {
      height: 25px;
      line-height: 25px;
      text-align: center;
      cursor: pointer;
    }

    .type-options {
      height: 50px;
      line-height: 50px;
      text-align: center;
      cursor: pointer;

      &:hover {
        background-color: #dfe4ed;
      }

      .pr5 {
        padding-right: 5px;
      }
    }

    .actBG {
      background-color: #dfe4ed;
      font-weight: bold;
    }

    .border-top {
      border-top: 1px solid #dfe4ed;
    }

    .move-line {
      position: absolute;
      top: 0;
      left: calc(100% - 2px);
      height: 100%;
      width: 1px;
      background: #dfe4ed;
      cursor: col-resize;
    }
  }

  .content-right {
    flex: 1;
    height: 100%;

    .main {
      height: calc(100% - 74px);
    }
  }
}

.map-toggle-out {
  position: relative;
  top: 45%;
  left: -20px;
  font-size: 20px;
  padding-top: 5px;
  margin-top: 10px;
  float: right;
  height: 30px;
  opacity: 0.9;
  background-color: white;
  cursor: pointer;
}
</style>
