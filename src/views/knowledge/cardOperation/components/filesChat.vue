<template>
</template>
<script>
import serviceKnowledge from "@/api/knowledge.js";
import userService from "@/api/techdocmanage/docCenter/user";
import { getConfigKey } from "@/api/system/config";
export default {
  name: '',
  components: {},
  props: {},
  data () {
    return {
        embed_model:''
    }
  },
  computed: {},
  watch: {},
  created () {
    getConfigKey("embed_model").then((response) => {
      this.embed_model = response.msg;
    });
  },
  mounted () {},
  methods: {
    initData(value){
            // 1查询是否存在选中文件的临时知识库 ： 
            //  1.1 存在    则查询临时知识库下的文件列表是否存在选中文件   
            //  1.1.2 存在  则打开对话框
            //  1.1.3 不存在  则再此临时知识库下重新上传此文件
            //  1.2 不存在 则再创建
            // 2创建临时知识库- template-Date.now()
            // 3下载文件获取文件流  将文件流转成file对象
            // 4将文件对象上传给临时知识库并进行概述
        this.queryFilesKnoledge(value)
    },
    // 查询是否存在选中文件的临时知识库
    async queryFilesKnoledge(value) {
      const files = value.map((v) => v.userDocName).join(",");
      const { code, data } = await serviceKnowledge.queryFilesKnoledge({
        files,
      });

      if (code === 200) {
        if (data.knowledge_name) {
          //  则查询临时知识库下的文件列表 
        const res = await serviceKnowledge.getKnowledgeFileList({
          knowledge_base_name: data.knowledge_name,
        });
        if (res.code === 200) {
          console.log(res);
        // 选中的文件名是否都存在临时知识库下的文件列表 
          let filesArr= files.split(",")
          const selectFileExistTemBase =  filesArr.every((v) => res.data.includes(v));
          // let missingFiles = filesArr.filter((v) => !res.data.includes(v));
          if (selectFileExistTemBase) {
                      this.$store.commit("common/CHATLOADING", false);
          this.$store.commit("common/CHAT", data.knowledge_name);
          this.$store.commit("common/FILES", files.split(","));
          }else{
              this.$store.commit("common/CHATLOADING", true);
              this.$store.commit("common/CHAT", data.knowledge_name);
              this.$store.commit("common/FILES", files.split(","));
              this.getFileObj(value, data.knowledge_name)
          }
        }else{
          this.createKnoledge(value,data.knowledge_name);
        }

          // this.getFileObj(value, data.knowledge_name);
        } else {
          this.createKnoledge(value);
        }
      }
    },
    // 创建知识库
    async createKnoledge(value,knowledge_name='') {
      const knowledge_base_name =knowledge_name?knowledge_name: `template-${Date.now()}`;
      const files = value.map((v) => v.userDocName);
      this.$store.commit("common/CHATLOADING", true);
      this.$store.commit("common/CHAT", knowledge_base_name);
      this.$store.commit("common/FILES", files);
      //新增临时知识库
      const res = await serviceKnowledge.insertKnowledgeDatabase({
        knowledge_name: knowledge_base_name,
        files: files.join(","),
      });
      

      const { code, data, msg } = await serviceKnowledge.addKnowledgeList({
        knowledge_base_name,
        vector_store_type: "faiss",
        // embed_model: "bge-large-zh-v1.5",
        embed_model: this.embed_model,
      });
      if (code === 200) {
        
        // 下载文件获取文件流  将文件流转成file对象
        this.getFileObj(value, knowledge_base_name);
      } else {
        this.$message.error(msg);
      }
    },
    // 下载文件获取文件流  将文件流转成file对象
    getFileObj(value, knowledge_base_name) {
      // 创建一个 Promise 数组
      let promises = [];

      const downloadSource = (data) => {
        // 返回一个新的 Promise
        return userService
          .downloadSourceFile(data)
          .then((res) => {
            let blob = new Blob([res]);

            // 直接将 Blob 转换为 File 对象，而不是先读取文本内容
            // 注意：这里假设 res 不是 JSON，而是直接的二进制数据
            const file = new File([blob], data.fileName, {
              type: blob.type,
              lastModified: Date.now(),
            });

            // 返回 File 对象
            return file;
          })
          .catch((error) => {
            // 如果出现错误，你可能想要在这里处理它
            console.error("Download failed:", error);
            // 返回一个拒绝状态的 Promise
            return Promise.reject(error);
          });
      };

      if (value && value.length) {
        // 为每个值创建并推送 Promise
        value.forEach((v) => {
          promises.push(
            downloadSource({
              id: v.docId.toString(),
              userId: this.$store.state.user.userId,
              fileName: v.userDocName,
            })
          );
        });

        // 使用 Promise.all 等待所有 Promise 都完成
        Promise.all(promises)
          .then((files) => {
            // files 现在是所有下载的文件对象的数组
            this.addKnoledgeFile(files, knowledge_base_name);
          })
          .catch((error) => {
            console.error(error);
          });
      }
    },
    // 向知识库中插入上传的文件文件
    async addKnoledgeFile(files, knowledge_base_name) {
      const setData = await serviceKnowledge.getChatSetting({
        knowledgeName: knowledge_base_name,
      });
      
      if (setData.code === 200) {
        let formData = new FormData();
        files.forEach((item) => {
          formData.append("files", item);
        });
        formData.append("knowledge_base_name", knowledge_base_name);
        formData.append("to_vector_store", true);
        formData.append("not_refresh_vs_cache", false);
        formData.append("chunk_size", setData.data[0].chunk_size);
        formData.append("chunk_overlap", 50);
        formData.append("zh_title_enhance", true);
        formData.append("override", true);
        const { code, data } = await serviceKnowledge.uploadFile(formData);
        if (code === 200) {
          this.$store.commit("common/CHATLOADING", false);
        }
      }
    },
  }
}
</script>

<style scoped lang="scss">
</style>
